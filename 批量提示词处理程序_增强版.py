import asyncio
import aiohttp
import aiofiles
import os
import tiktoken
import time
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import filedialog

API_KEY = 'sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65'
BASE_URL = "https://free.v36.cm/v1/"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json",
}

# 系统提示词  
SYSTEM_PROMPT = """You are a Midjourney prompt optimizer, tasked with making creatively rich modifications to prompts while ensuring quality is maintained.

Rules:

Replace the main subject with a creative alternative (keeping the same style/mood), and at the same time, creatively adjust more descriptive elements such as actions, scene layouts, decorative details, etc., to increase the range of modifications.
Keep all original parameters (--ar, --v, etc.) exactly unchanged.
Retain core detailed descriptions (lighting, textures, colors, materials, style, etc.), allowing for expansive modifications to non-core details.
Ensure "on white background" is included (add if missing).
Output only a single line, with no explanations.

Examples:
Input: "a cute cat sitting, soft lighting --ar 1:1"
Output: "a chubby rabbit hugging a carrot, curled up on a cushion, soft lighting on white background --ar 1:1"

Input: "ornate castle with rich architectural details, fantasy style --v 5 --ar 2:3"
Output: "ornate floating palace adorned with glowing vines, architectural details as intricate as a maze, fantasy style on white background --v 5 --ar 2:3"

Transform the following prompt:"""

# 全局变量用于统计
processed_count = 0
success_count = 0
error_count = 0
total_input_tokens = 0
total_output_tokens = 0
total_cost = 0.0
start_time = None
total_prompts = 0

# 速率限制配置
MAX_REQUESTS_PER_MINUTE = 96
request_times = []

# 价格配置（每1000个token的价格）
INPUT_PRICE_PER_1K = 0.00015  # $0.00015 / 1k tokens
OUTPUT_PRICE_PER_1K = 0.0006  # $0.0006 / 1k tokens

# 初始化tokenizer
try:
    tokenizer = tiktoken.encoding_for_model("gpt-4o-mini")
except:
    tokenizer = tiktoken.get_encoding("cl100k_base")

def count_tokens(text):
    """计算文本的token数量"""
    return len(tokenizer.encode(text))

def calculate_cost(input_tokens, output_tokens):
    """计算API调用成本"""
    input_cost = (input_tokens / 1000) * INPUT_PRICE_PER_1K
    output_cost = (output_tokens / 1000) * OUTPUT_PRICE_PER_1K
    return input_cost + output_cost

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{int(secs)}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{int(hours)}小时{int(minutes)}分{int(secs)}秒"

def calculate_eta(processed, total, elapsed_time):
    """计算预计完成时间"""
    if processed == 0:
        return "计算中..."
    
    avg_time_per_item = elapsed_time / processed
    remaining_items = total - processed
    remaining_time = avg_time_per_item * remaining_items
    
    eta = datetime.now() + timedelta(seconds=remaining_time)
    return eta.strftime("%H:%M:%S")

def display_progress_stats():
    """显示进度统计信息"""
    global start_time, processed_count, success_count, error_count, total_cost, total_prompts
    
    if start_time is None:
        return
    
    elapsed_time = time.time() - start_time
    progress_percent = (processed_count / total_prompts) * 100 if total_prompts > 0 else 0
    success_rate = (success_count / processed_count) * 100 if processed_count > 0 else 0
    
    # 计算处理速度（每分钟）
    speed_per_minute = (processed_count / elapsed_time) * 60 if elapsed_time > 0 else 0
    
    # 预计完成时间
    eta = calculate_eta(processed_count, total_prompts, elapsed_time)
    
    # 剩余时间估算
    if processed_count > 0 and processed_count < total_prompts:
        remaining_time = ((total_prompts - processed_count) / processed_count) * elapsed_time
        remaining_str = format_time(remaining_time)
    else:
        remaining_str = "即将完成"
    
    print(f"\n📊 实时统计 (第{processed_count}个)")
    print(f"   进度: {progress_percent:.1f}% ({processed_count}/{total_prompts})")
    print(f"   成功率: {success_rate:.1f}% (成功:{success_count}, 失败:{error_count})")
    print(f"   处理速度: {speed_per_minute:.1f}个/分钟")
    print(f"   累计成本: ${total_cost:.6f}")
    print(f"   已运行: {format_time(elapsed_time)}")
    print(f"   剩余时间: {remaining_str}")
    print(f"   预计完成: {eta}")
    print("-" * 60)

def select_input_file():
    """选择输入文件"""
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    root.attributes('-topmost', True)  # 设置置顶
    
    # 打开文件选择对话框
    input_file = filedialog.askopenfilename(
        title="选择提示词文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
        parent=root
    )
    
    root.destroy()  # 销毁根窗口
    return input_file

async def rate_limit():
    """速率限制控制"""
    global request_times
    current_time = time.time()
    
    # 清理超过1分钟的请求记录
    request_times = [t for t in request_times if current_time - t < 60]
    
    # 如果请求数量达到限制，等待
    if len(request_times) >= MAX_REQUESTS_PER_MINUTE:
        sleep_time = 60 - (current_time - request_times[0])
        if sleep_time > 0:
            print(f"达到速率限制，等待 {sleep_time:.1f} 秒...")
            await asyncio.sleep(sleep_time)
            # 重新清理请求记录
            current_time = time.time()
            request_times = [t for t in request_times if current_time - t < 60]
    
    # 记录当前请求时间
    request_times.append(current_time)

async def process_prompt(session, semaphore, prompt, output_file):
    """处理单个提示词"""
    global processed_count, success_count, error_count, total_input_tokens, total_output_tokens, total_cost
    
    async with semaphore:
        try:
            # 速率限制
            await rate_limit()
            
            # 计算输入token数量
            system_tokens = count_tokens(SYSTEM_PROMPT)
            user_tokens = count_tokens(prompt.strip())
            input_tokens = system_tokens + user_tokens
            
            async with session.post(
                url=f"{BASE_URL}chat/completions",
                json={
                    "model": "gpt-4o-mini",
                    "max_tokens": 4000,
                    "temperature": 0.5,
                    "messages": [
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": prompt.strip()}
                    ],
                },
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    ai_response = result['choices'][0]['message']['content']
                    
                    # 计算输出token数量
                    output_tokens = count_tokens(ai_response)
                    
                    # 计算本次请求成本
                    request_cost = calculate_cost(input_tokens, output_tokens)
                    
                    # 更新全局统计
                    total_input_tokens += input_tokens
                    total_output_tokens += output_tokens
                    total_cost += request_cost
                    
                    # 只记录AI回复内容
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"{ai_response}\n")
                    
                    success_count += 1
                    
                else:
                    error_msg = f"请求失败，状态码: {response.status}"
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"错误: {error_msg}\n")
                    
                    error_count += 1
                    print(f"处理第 {processed_count + 1} 个提示词失败: {error_msg}")
                    
        except Exception as e:
            error_msg = f"请求发生异常: {e}"
            async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                await f.write(f"错误: {error_msg}\n")
            
            error_count += 1
            print(f"处理第 {processed_count + 1} 个提示词异常: {error_msg}")
        
        finally:
            processed_count += 1
            
            # 每处理100个显示进度统计
            if processed_count % 100 == 0:
                display_progress_stats()

async def main():
    """主程序"""
    global processed_count, success_count, error_count, total_input_tokens, total_output_tokens, total_cost
    global start_time, total_prompts
    
    # 选择输入文件
    input_file = select_input_file()
    if not input_file:
        print("❌ 未选择文件，程序退出！")
        return
    
    if not os.path.exists(input_file):
        print(f"❌ 选择的文件 {input_file} 不存在！")
        return
    
    # 创建输出文件名（带时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"AI优化结果_{timestamp}.txt"
    
    # 读取所有提示词
    async with aiofiles.open(input_file, 'r', encoding='utf-8') as f:
        content = await f.read()
        prompts = [line.strip() for line in content.split('\n') if line.strip()]
    
    total_prompts = len(prompts)
    start_time = time.time()
    start_datetime = datetime.now()
    
    print(f"🚀 批量提示词处理程序启动")
    print(f"选择的文件: {input_file}")
    print(f"读取到 {total_prompts} 个提示词")
    print(f"结果将保存到: {output_file}")
    print(f"开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"速率限制: 每分钟最多 {MAX_REQUESTS_PER_MINUTE} 次请求")
    print(f"使用最大 10 个并发线程")  
    print("=" * 80)
    
    # 创建输出文件
    async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
        await f.write("")  # 创建空文件
    
    # 创建信号量限制并发数为10（配合速率限制）
    semaphore = asyncio.Semaphore(10)
    
    # 创建HTTP会话
    async with aiohttp.ClientSession() as session:
        # 创建所有任务
        tasks = [
            process_prompt(session, semaphore, prompt, output_file) 
            for prompt in prompts
        ]
        
        # 执行所有任务
        await asyncio.gather(*tasks)
    
    # 计算总运行时长
    end_time = time.time()
    total_runtime = end_time - start_time
    end_datetime = datetime.now()
    
    print("=" * 80)
    print(f"🎉 批量处理完成！")
    print(f"📈 最终统计:")
    print(f"   开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   结束时间: {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   总运行时长: {format_time(total_runtime)}")
    print(f"   总数量: {total_prompts}")
    print(f"   成功: {success_count}")
    print(f"   失败: {error_count}")
    print(f"   成功率: {(success_count/total_prompts)*100:.2f}%")
    print(f"   平均速度: {(total_prompts/total_runtime)*60:.1f}个/分钟")
    print(f"   总输入tokens: {total_input_tokens:,}")
    print(f"   总输出tokens: {total_output_tokens:,}")
    print(f"   总成本: ${total_cost:.6f}")
    print(f"📁 结果已保存到: {output_file}")

if __name__ == "__main__":
    asyncio.run(main())