import asyncio
import aiohttp
import aiofiles
import os
import tiktoken
import time
from datetime import datetime
import tkinter as tk
from tkinter import filedialog

API_KEY = 'sk-YxSs4swafKikaWubFc783534C45f41078a90B139A75872F9'
BASE_URL = "https://api.gpt.ge/v1/"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json",
}

# 系统提示词  
SYSTEM_PROMPT = """You are a Midjourney prompt optimizer, tasked with making creatively rich modifications to prompts while ensuring quality is maintained.

Rules:

Replace the main subject with a creative alternative (keeping the same style/mood), and at the same time, creatively adjust more descriptive elements such as actions, scene layouts, decorative details, etc., to increase the range of modifications.
Keep all original parameters (--ar, --v, etc.) exactly unchanged.
Retain core detailed descriptions (lighting, textures, colors, materials, style, etc.), allowing for expansive modifications to non-core details.
Ensure "on white background" is included (add if missing).
Output only a single line, with no explanations.

Examples:
Input: "a cute cat sitting, soft lighting --ar 1:1"
Output: "a chubby rabbit hugging a carrot, curled up on a cushion, soft lighting on white background --ar 1:1"

Input: "ornate castle with rich architectural details, fantasy style --v 5 --ar 2:3"
Output: "ornate floating palace adorned with glowing vines, architectural details as intricate as a maze, fantasy style on white background --v 5 --ar 2:3"

Transform the following prompt:"""

# 全局变量用于统计
processed_count = 0
success_count = 0
error_count = 0
total_input_tokens = 0
total_output_tokens = 0
total_cost = 0.0

# 价格配置（每1000个token的价格）
INPUT_PRICE_PER_1K = 0.00015  # $0.00015 / 1k tokens
OUTPUT_PRICE_PER_1K = 0.0006  # $0.0006 / 1k tokens

# 初始化tokenizer
try:
    tokenizer = tiktoken.encoding_for_model("gpt-4o-mini")
except:
    tokenizer = tiktoken.get_encoding("cl100k_base")

def count_tokens(text):
    """计算文本的token数量"""
    return len(tokenizer.encode(text))

def calculate_cost(input_tokens, output_tokens):
    """计算API调用成本"""
    input_cost = (input_tokens / 1000) * INPUT_PRICE_PER_1K
    output_cost = (output_tokens / 1000) * OUTPUT_PRICE_PER_1K
    return input_cost + output_cost

def select_input_file():
    """选择输入文件"""
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    root.attributes('-topmost', True)  # 设置置顶
    
    # 打开文件选择对话框
    input_file = filedialog.askopenfilename(
        title="选择提示词文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
        parent=root
    )
    
    root.destroy()  # 销毁根窗口
    return input_file

async def process_prompt(session, semaphore, prompt, output_file):
    """处理单个提示词"""
    global processed_count, success_count, error_count, total_input_tokens, total_output_tokens, total_cost
    
    async with semaphore:
        try:
            # 计算输入token数量
            system_tokens = count_tokens(SYSTEM_PROMPT)
            user_tokens = count_tokens(prompt.strip())
            input_tokens = system_tokens + user_tokens
            
            async with session.post(
                url=f"{BASE_URL}chat/completions",
                json={
                    "model": "gpt-4o-mini",
                    "max_tokens": 4000,
                    "temperature": 0.5,
                    "messages": [
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": prompt.strip()}
                    ],
                },
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    ai_response = result['choices'][0]['message']['content']
                    
                    # 计算输出token数量
                    output_tokens = count_tokens(ai_response)
                    
                    # 计算本次请求成本
                    request_cost = calculate_cost(input_tokens, output_tokens)
                    
                    # 更新全局统计
                    total_input_tokens += input_tokens
                    total_output_tokens += output_tokens
                    total_cost += request_cost
                    
                    # 实时显示处理进度
                    print(f"第 {processed_count + 1} 个提示词处理完成")
                    
                    # 只记录AI回复内容
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"{ai_response}\n")
                    
                    success_count += 1
                    
                else:
                    error_msg = f"请求失败，状态码: {response.status}"
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"错误: {error_msg}\n")
                    
                    error_count += 1
                    print(f"处理第 {processed_count + 1} 个提示词失败: {error_msg}")
                    
        except Exception as e:
            error_msg = f"请求发生异常: {e}"
            async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                await f.write(f"错误: {error_msg}\n")
            
            error_count += 1
            print(f"处理第 {processed_count + 1} 个提示词异常: {error_msg}")
        
        finally:
            processed_count += 1

async def main():
    """主程序"""
    global processed_count, success_count, error_count, total_input_tokens, total_output_tokens, total_cost
    
    # 选择输入文件
    input_file = select_input_file()
    if not input_file:
        print("❌ 未选择文件，程序退出！")
        return
    
    if not os.path.exists(input_file):
        print(f"❌ 选择的文件 {input_file} 不存在！")
        return
    
    # 创建输出文件名（带时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"AI优化结果_{timestamp}.txt"
    
    # 读取所有提示词
    async with aiofiles.open(input_file, 'r', encoding='utf-8') as f:
        content = await f.read()
        prompts = [line.strip() for line in content.split('\n') if line.strip()]
    
    print(f"选择的文件: {input_file}")
    print(f"读取到 {len(prompts)} 个提示词")
    print(f"结果将保存到: {output_file}")
    print(f"无速率限制版本")
    print(f"使用最大 200 个并发线程")  
    print("=" * 80)
    
    # 创建输出文件
    async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
        await f.write("")  # 创建空文件
    
    # 创建信号量限制并发数为200
    semaphore = asyncio.Semaphore(200)
    
    # 创建HTTP会话
    async with aiohttp.ClientSession() as session:
        # 创建所有任务
        tasks = [
            process_prompt(session, semaphore, prompt, output_file) 
            for prompt in prompts
        ]
        
        # 执行所有任务
        await asyncio.gather(*tasks)
    
    print("=" * 80)
    print(f"批量处理完成！")
    print(f"处理统计:")
    print(f"   总数量: {len(prompts)}")
    print(f"   成功: {success_count}")
    print(f"   失败: {error_count}")
    print(f"   总成本: ${total_cost:.6f}")
    print(f"结果已保存到: {output_file}")

if __name__ == "__main__":
    asyncio.run(main())